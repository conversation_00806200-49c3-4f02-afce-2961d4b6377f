from PyQt6.QtWidgets import (
    QWidget, QFormLayout, QVBoxLayout, QLineEdit, QPushButton, QComboBox,
    QDateEdit, QMessageBox, QHBoxLayout, QDialog, QListWidget, QTableWidget, QTableWidgetItem
)
from PyQt6.QtCore import QDate, Qt
from db import get_connection

class ConsUniteMTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 350)

        main_layout = QVBoxLayout()
        form = QFormLayout()
        form.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        form.setFormAlignment(Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop)
        form.setHorizontalSpacing(20)
        form.setVerticalSpacing(12)

        # Ligne pour nom + bouton auto-complétion
        nom_layout = QHBoxLayout()
        self.nom_input = QComboBox()
        self.nom_input.setEditable(True)
        self.load_noms()
        self.autocomplete_btn = QPushButton("Auto-complétion")
        self.autocomplete_btn.clicked.connect(self.show_autocomplete)
        nom_layout.addWidget(self.nom_input)
        nom_layout.addWidget(self.autocomplete_btn)

        self.gasoil_input = QLineEdit()
        self.essence_input = QLineEdit()
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        self.rubrique_input = QLineEdit()
        self.id_depot_input = QLineEdit()

        form.addRow("Nom unité :", nom_layout)
        form.addRow("Gasoil :", self.gasoil_input)
        form.addRow("Essence :", self.essence_input)
        form.addRow("Date consommation :", self.date_input)
        form.addRow("Rubrique :", self.rubrique_input)
        form.addRow("ID Dépôt :", self.id_depot_input)

        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        self.submit_btn = QPushButton("Ajouter")
        btn_layout.addWidget(self.submit_btn)
        btn_layout.addStretch()
        self.submit_btn.clicked.connect(self.ajouter_consom_uniteM)
        self.show_btn = QPushButton("Afficher consommation")
        btn_layout.addWidget(self.show_btn)
        self.show_btn.clicked.connect(self.afficher_consom_uniteM)
        self.delete_btn = QPushButton("Supprimer")
        btn_layout.addWidget(self.delete_btn)
        self.delete_btn.clicked.connect(self.supprimer_consom_uniteM)
        self.edit_btn = QPushButton("Modifier")
        btn_layout.addWidget(self.edit_btn)
        self.edit_btn.clicked.connect(self.modifier_consom_uniteM)

        main_layout.addLayout(form)
        main_layout.addLayout(btn_layout)
        self.setLayout(main_layout)

    def load_noms(self):
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT nom FROM Consom_uniteM")
            noms = [row[0] for row in cursor.fetchall()]
            self.nom_input.clear()
            self.nom_input.addItems(noms)
            cursor.close()
            conn.close()
        except Exception:
            pass

    def show_autocomplete(self):
        self.nom_input.showPopup()

    def ajouter_consom_uniteM(self):
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO Consom_uniteM
                (nom, gasoil, essence, date_Cons, rubrique, id_depot)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                self.nom_input.currentText(),
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.date_input.date().toString('yyyy-MM-dd'),
                self.rubrique_input.text(),
                self.id_depot_input.text()
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Succès", "Consommation unité M ajoutée !")
            self.gasoil_input.clear()
            self.essence_input.clear()
            self.id_depot_input.clear()
            self.rubrique_input.clear()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def afficher_consom_uniteM(self):
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT 
                    c.date_Cons,
                    c.gasoil,
                    c.essence,
                    d.nom AS depot_nom,
                    r.nom AS rubrique_nom,
                    m.nom AS mission_nom,
                    c.rubrique,
                    c.id_depot
                FROM Consom_uniteM c
                LEFT JOIN Depot d ON c.id_depot = d.id_depot
                LEFT JOIN Rubrique r ON c.rubrique = r.nom
                LEFT JOIN Rubrique_Mission rm ON rm.id_rubrique = r.id_rubrique
                LEFT JOIN Mission m ON rm.id_mission = m.id_mission
                WHERE c.nom = %s
                ORDER BY c.date_Cons DESC
            """, (self.nom_input.currentText(),))
            results = cursor.fetchall()
            cursor.close()
            conn.close()
            if not results:
                QMessageBox.information(self, "Consommation Unité M", "Aucune consommation trouvée pour cette unité.")
                return

            # Création de la fenêtre de sélection avec tableau
            dialog = QDialog(self)
            dialog.setWindowTitle("Sélectionnez une consommation")
            layout = QVBoxLayout(dialog)
            table = QTableWidget(len(results), 7)
            table.setHorizontalHeaderLabels([
                "Date", "Gasoil", "Essence", "Dépôt", "Rubrique", "Mission", "ID Dépôt"
            ])
            items = []
            for row_idx, row in enumerate(results):
                date_cons, gasoil, essence, depot_nom, rubrique_nom, mission_nom, rubrique, id_depot = row
                table.setItem(row_idx, 0, QTableWidgetItem(str(date_cons)))
                table.setItem(row_idx, 1, QTableWidgetItem(str(gasoil)))
                table.setItem(row_idx, 2, QTableWidgetItem(str(essence)))
                table.setItem(row_idx, 3, QTableWidgetItem(str(depot_nom)))
                table.setItem(row_idx, 4, QTableWidgetItem(str(rubrique_nom) if rubrique_nom else str(rubrique)))
                table.setItem(row_idx, 5, QTableWidgetItem(str(mission_nom if mission_nom else "N/A")))
                table.setItem(row_idx, 6, QTableWidgetItem(str(id_depot)))
                items.append((date_cons, rubrique, id_depot))
            table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
            layout.addWidget(table)

            btn_layout = QHBoxLayout()
            btn_suppr = QPushButton("Supprimer")
            btn_modif = QPushButton("Modifier")
            btn_layout.addWidget(btn_suppr)
            btn_layout.addWidget(btn_modif)
            layout.addLayout(btn_layout)

            def supprimer():
                idx = table.currentRow()
                if idx < 0:
                    QMessageBox.warning(dialog, "Sélection", "Sélectionnez une consommation à supprimer.")
                    return
                date_cons, rubrique, id_depot = items[idx]
                try:
                    conn = get_connection()
                    cursor = conn.cursor()
                    cursor.execute("""
                        DELETE FROM Consom_uniteM
                        WHERE nom=%s AND date_Cons=%s AND rubrique=%s AND id_depot=%s
                    """, (self.nom_input.currentText(), date_cons, rubrique, id_depot))
                    conn.commit()
                    cursor.close()
                    conn.close()
                    QMessageBox.information(dialog, "Suppression", "Consommation supprimée avec succès.")
                    dialog.accept()
                except Exception as e:
                    QMessageBox.critical(dialog, "Erreur", str(e))

            def modifier():
                idx = table.currentRow()
                if idx < 0:
                    QMessageBox.warning(dialog, "Sélection", "Sélectionnez une consommation à modifier.")
                    return
                date_cons, rubrique_base, id_depot_base = items[idx]
                gasoil = table.item(idx, 1).text()
                essence = table.item(idx, 2).text()
                id_depot_new = table.item(idx, 6).text()
                rubrique_new = table.item(idx, 4).text()
                mission_nom_new = table.item(idx, 5).text()

                if not rubrique_new or rubrique_new == "None":
                    QMessageBox.warning(dialog, "Rubrique", "Veuillez saisir ou sélectionner une rubrique valide.")
                    return

                try:
                    conn = get_connection()
                    cursor = conn.cursor()
                    cursor.execute("SELECT id_mission FROM Mission WHERE nom=%s", (mission_nom_new,))
                    mission_row = cursor.fetchone()
                    if not mission_row:
                        QMessageBox.warning(dialog, "Mission", "Mission inconnue : " + mission_nom_new)
                        return
                    id_mission_new = mission_row[0]
                    cursor.execute("SELECT id_rubrique FROM Rubrique WHERE nom=%s", (rubrique_new,))
                    rubrique_row = cursor.fetchone()
                    if not rubrique_row:
                        QMessageBox.warning(dialog, "Rubrique", "Rubrique inconnue : " + rubrique_new)
                        return
                    id_rubrique_new = rubrique_row[0]
                    # Mettre à jour la consommation
                    cursor.execute("""
                        UPDATE Consom_uniteM
                        SET gasoil=%s, essence=%s, id_depot=%s, rubrique=%s
                        WHERE nom=%s AND date_Cons=%s AND rubrique=%s AND id_depot=%s
                    """, (
                        gasoil,
                        essence,
                        id_depot_new,
                        rubrique_new,
                        self.nom_input.currentText(),
                        date_cons,
                        rubrique_base,
                        id_depot_base
                    ))
                    # Mettre à jour la liaison Rubrique_Mission
                    cursor.execute("""
                        UPDATE Rubrique_Mission
                        SET id_mission=%s
                        WHERE id_rubrique=%s
                    """, (id_mission_new, id_rubrique_new))
                    conn.commit()
                    cursor.close()
                    conn.close()
                    QMessageBox.information(dialog, "Modification", "Consommation et mission modifiées avec succès.")
                    dialog.accept()
                except Exception as e:
                    QMessageBox.critical(dialog, "Erreur", str(e))

            btn_suppr.clicked.connect(supprimer)
            btn_modif.clicked.connect(modifier)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def supprimer_consom_uniteM(self):
        # Suppression basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                DELETE FROM Consom_uniteM
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (nom, date_cons, rubrique))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Suppression", "Consommation supprimée avec succès.")
            self.gasoil_input.clear()
            self.essence_input.clear()
            self.id_depot_input.clear()
            self.rubrique_input.clear()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%s, essence=%s, id_depot=%s
                WHERE nom=%s AND date_Cons=%s AND rubrique=%s
            """, (
                self.gasoil_input.text(),
                self.essence_input.text(),
                self.id_depot_input.text(),
                nom,
                date_cons,
                rubrique
            ))
            conn.commit()
            cursor.close()
            conn.close()
            QMessageBox.information(self, "Modification", "Consommation modifiée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", str(e))

    def modifier_consom_uniteM(self):
        # Modification basée sur nom, date et rubrique
        nom = self.nom_input.currentText()
        date_cons = self.date_input.date().toString('yyyy-MM-dd')
        rubrique = self.rubrique_input.text()
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE Consom_uniteM
                SET gasoil=%